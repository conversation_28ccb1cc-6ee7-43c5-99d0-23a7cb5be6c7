import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';

/// Authentication screen with biometric prompt and fallback options
/// This is the entry point for user authentication
class AuthScreen extends StatefulWidget {
  const AuthScreen({Key? key}) : super(key: key);

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _checkBiometricAvailability();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _checkBiometricAvailability() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.refreshBiometricInfo();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.8),
              Theme.of(context).primaryColor.withValues(alpha: 0.6),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildAuthContent(),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildAuthContent() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildAppLogo(),
              const SizedBox(height: 48),
              _buildWelcomeText(),
              const SizedBox(height: 32),
              _buildAuthStatusCard(authProvider),
              const SizedBox(height: 32),
              _buildAuthButton(authProvider),
              if (authProvider.errorMessage != null) ...[
                const SizedBox(height: 16),
                _buildErrorMessage(authProvider.errorMessage!),
              ],
              const SizedBox(height: 24),
              _buildDebugSection(authProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAppLogo() {
    return Column(
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            Icons.security,
            size: 50,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Secure App',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildWelcomeText() {
    return Column(
      children: [
        Text(
          'Welcome Back',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Please authenticate to access your secure data',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.white.withValues(alpha: 0.9),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAuthStatusCard(AuthProvider authProvider) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              _getStatusIcon(authProvider),
              size: 48,
              color: _getStatusColor(authProvider),
            ),
            const SizedBox(height: 16),
            Text(
              authProvider.getAuthStatusMessage(),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (authProvider.biometricAvailable && authProvider.biometricEnrolled) ...[
              const SizedBox(height: 8),
              Text(
                'Tap the button below to authenticate',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAuthButton(AuthProvider authProvider) {
    if (!authProvider.canAuthenticate()) {
      return _buildSettingsButton();
    }

    return ElevatedButton.icon(
      onPressed: authProvider.isLoading ? null : () => _authenticate(authProvider),
      icon: authProvider.isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Icon(_getBiometricIcon(authProvider)),
      label: Text(
        authProvider.isLoading 
            ? 'Authenticating...' 
            : 'Authenticate with ${authProvider.biometricDescription}',
      ),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 4,
      ),
    );
  }

  Widget _buildSettingsButton() {
    return OutlinedButton.icon(
      onPressed: () => _showSettingsDialog(),
      icon: const Icon(Icons.settings),
      label: const Text('Open Settings'),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        side: const BorderSide(color: Colors.white, width: 2),
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildErrorMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: Colors.red),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDebugSection(AuthProvider authProvider) {
    // Only show in debug mode
    if (!const bool.fromEnvironment('dart.vm.product')) {
      return Column(
        children: [
          const Divider(color: Colors.white54),
          const SizedBox(height: 16),
          Text(
            'Debug Options',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => authProvider.simulateAuthentication(),
            child: const Text(
              'Simulate Authentication',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () => _showBiometricInfo(authProvider),
            child: const Text(
              'Show Biometric Info',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () => _testWeakBiometric(authProvider),
            child: const Text(
              'Test Weak Biometric',
              style: TextStyle(color: Colors.white70),
            ),
          ),
        ],
      );
    }
    return const SizedBox.shrink();
  }

  // Helper methods
  IconData _getStatusIcon(AuthProvider authProvider) {
    if (!authProvider.biometricAvailable) return Icons.error_outline;
    if (!authProvider.biometricEnrolled) return Icons.warning_amber;
    return Icons.fingerprint;
  }

  Color _getStatusColor(AuthProvider authProvider) {
    if (!authProvider.biometricAvailable) return Colors.red;
    if (!authProvider.biometricEnrolled) return Colors.orange;
    return Colors.green;
  }

  IconData _getBiometricIcon(AuthProvider authProvider) {
    if (authProvider.biometricDescription.toLowerCase().contains('face')) {
      return Icons.face;
    }
    return Icons.fingerprint;
  }

  Future<void> _authenticate(AuthProvider authProvider) async {
    final success = await authProvider.authenticateWithBiometrics();
    
    if (!success && mounted) {
      // Show additional help if authentication failed
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(authProvider.errorMessage ?? 'Authentication failed'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: () => _authenticate(authProvider),
          ),
        ),
      );
    }
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Biometric Setup Required'),
        content: const Text(
          'To use this app, you need to set up biometric authentication (fingerprint or face recognition) in your device settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Debug methods
  Future<void> _showBiometricInfo(AuthProvider authProvider) async {
    final info = await authProvider.getBiometricInfo();
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Biometric Information'),
          content: SingleChildScrollView(
            child: Text(info.toString()),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  Future<void> _testWeakBiometric(AuthProvider authProvider) async {
    try {
      final biometricService = authProvider.biometricService;
      final result = await biometricService.authenticateWithWeakBiometrics(
        localizedReason: 'Testing weak biometric authentication',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result.isSuccess
                  ? 'Weak biometric test successful!'
                  : 'Weak biometric test failed: ${result.errorMessage}',
            ),
            backgroundColor: result.isSuccess ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error testing weak biometric: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
