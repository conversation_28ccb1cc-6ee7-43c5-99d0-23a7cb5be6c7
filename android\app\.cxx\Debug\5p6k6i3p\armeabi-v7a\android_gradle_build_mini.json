{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "B:\\BE-D2D\\BE\\Be-sem_7\\project auth\\android\\app\\.cxx\\Debug\\5p6k6i3p\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "B:\\BE-D2D\\BE\\Be-sem_7\\project auth\\android\\app\\.cxx\\Debug\\5p6k6i3p\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}