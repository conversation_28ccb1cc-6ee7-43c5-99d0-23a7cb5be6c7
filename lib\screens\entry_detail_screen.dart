import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/secure_entry.dart';
import 'add_entry_screen.dart';

/// Screen for viewing detailed information about a secure entry
/// Includes copy functionality and edit/delete options
class EntryDetailScreen extends StatefulWidget {
  final SecureEntry entry;

  const EntryDetailScreen({Key? key, required this.entry}) : super(key: key);

  @override
  State<EntryDetailScreen> createState() => _EntryDetailScreenState();
}

class _EntryDetailScreenState extends State<EntryDetailScreen> {
  bool _obscureContent = false;

  @override
  void initState() {
    super.initState();
    // Obscure content by default for credential entries
    _obscureContent = widget.entry.type == SecureEntryType.loginCredentials ||
                      widget.entry.type == SecureEntryType.workCredentials;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.entry.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editEntry,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'copy_title',
                child: ListTile(
                  leading: Icon(Icons.copy),
                  title: Text('Copy Title'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'copy_content',
                child: ListTile(
                  leading: Icon(Icons.copy),
                  title: Text('Copy Content'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('Share'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTypeCard(),
          const SizedBox(height: 16),
          _buildTitleCard(),
          const SizedBox(height: 16),
          _buildContentCard(),
          const SizedBox(height: 16),
          _buildMetadataCard(),
        ],
      ),
    );
  }

  Widget _buildTypeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  widget.entry.type.icon,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Entry Type',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.entry.type.displayName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Title',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.copy, size: 20),
                  onPressed: () => _copyToClipboard(widget.entry.title, 'Title'),
                  tooltip: 'Copy title',
                ),
              ],
            ),
            const SizedBox(height: 8),
            SelectableText(
              widget.entry.title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getContentLabel(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.entry.type == SecureEntryType.loginCredentials ||
                        widget.entry.type == SecureEntryType.workCredentials)
                      IconButton(
                        icon: Icon(
                          _obscureContent ? Icons.visibility : Icons.visibility_off,
                          size: 20,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscureContent = !_obscureContent;
                          });
                        },
                        tooltip: _obscureContent ? 'Show content' : 'Hide content',
                      ),
                    IconButton(
                      icon: const Icon(Icons.copy, size: 20),
                      onPressed: () => _copyToClipboard(widget.entry.content, 'Content'),
                      tooltip: 'Copy content',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: SelectableText(
                _obscureContent ? _obscureText(widget.entry.content) : widget.entry.content,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontFamily: (widget.entry.type == SecureEntryType.loginCredentials ||
                              widget.entry.type == SecureEntryType.workCredentials) ? 'monospace' : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildMetadataRow('Created', _formatDateTime(widget.entry.createdAt)),
            const SizedBox(height: 8),
            _buildMetadataRow('Last Updated', _formatDateTime(widget.entry.updatedAt)),
            if (widget.entry.id != null) ...[
              const SizedBox(height: 8),
              _buildMetadataRow('Entry ID', widget.entry.id.toString()),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  // Helper methods
  String _getContentLabel() {
    switch (widget.entry.type) {
      case SecureEntryType.personalNote:
        return 'Personal Note';
      case SecureEntryType.loginCredentials:
        return 'Login Credentials';
      case SecureEntryType.paymentCard:
        return 'Payment Card Details';
      case SecureEntryType.identityDocument:
        return 'Identity Document';
      case SecureEntryType.secureNote:
        return 'Secure Note';
      case SecureEntryType.bankAccount:
        return 'Bank Account Details';
      case SecureEntryType.socialMedia:
        return 'Social Media Account';
      case SecureEntryType.workCredentials:
        return 'Work Credentials';
      case SecureEntryType.personalInfo:
        return 'Personal Information';
      case SecureEntryType.other:
        return 'Content';
    }
  }

  String _obscureText(String text) {
    return '•' * text.length;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$label copied to clipboard'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'copy_title':
        _copyToClipboard(widget.entry.title, 'Title');
        break;
      case 'copy_content':
        _copyToClipboard(widget.entry.content, 'Content');
        break;
      case 'share':
        _shareEntry();
        break;
    }
  }

  void _shareEntry() {
    // Note: In a real app, you might want to be more careful about sharing sensitive data
    final shareText = '''
${widget.entry.title}

Type: ${widget.entry.type.displayName}
Content: ${(widget.entry.type == SecureEntryType.loginCredentials ||
           widget.entry.type == SecureEntryType.workCredentials) ? '[Hidden for security]' : widget.entry.content}

Created: ${_formatDateTime(widget.entry.createdAt)}
''';

    // For now, just copy to clipboard as sharing sensitive data should be done carefully
    _copyToClipboard(shareText, 'Entry details');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Entry'),
        content: const Text(
          'Entry details have been copied to clipboard. Please be careful when sharing sensitive information.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _editEntry() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEntryScreen(entryToEdit: widget.entry),
      ),
    ).then((result) {
      // Refresh the screen if entry was updated
      if (result == true) {
        // In a real app, you might want to refresh the entry data
        setState(() {});
      }
    });
  }
}
