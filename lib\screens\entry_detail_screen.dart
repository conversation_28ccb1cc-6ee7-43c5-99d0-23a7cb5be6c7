import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/secure_entry.dart';
import '../theme/app_theme.dart';
import '../widgets/custom_widgets.dart';
import 'add_entry_screen.dart';

/// Screen for viewing detailed information about a secure entry
/// Includes copy functionality and edit/delete options
class EntryDetailScreen extends StatefulWidget {
  final SecureEntry entry;

  const EntryDetailScreen({Key? key, required this.entry}) : super(key: key);

  @override
  State<EntryDetailScreen> createState() => _EntryDetailScreenState();
}

class _EntryDetailScreenState extends State<EntryDetailScreen> {
  bool _obscureContent = false;

  @override
  void initState() {
    super.initState();
    // Obscure content by default for credential entries
    _obscureContent = widget.entry.type == SecureEntryType.loginCredentials ||
                      widget.entry.type == SecureEntryType.workCredentials;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.primaryGradient,
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.lightGray,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                  ),
                  child: _buildBody(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final color = Color(int.parse(widget.entry.type.colorHex.substring(1), radix: 16) + 0xFF000000);

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back_ios_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                widget.entry.type.icon,
                style: const TextStyle(
                  fontSize: 24,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.entry.title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.entry.type.displayName,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            icon: const Icon(
              Icons.more_vert,
              color: Colors.white,
              size: 24,
            ),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'copy_title',
                child: Row(
                  children: [
                    Icon(Icons.copy, size: 18),
                    SizedBox(width: 12),
                    Text('Copy Title'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'copy_content',
                child: Row(
                  children: [
                    Icon(Icons.copy_all, size: 18),
                    SizedBox(width: 12),
                    Text('Copy Content'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 18),
                    SizedBox(width: 12),
                    Text('Edit Entry'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share, size: 18),
                    SizedBox(width: 12),
                    Text('Share'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          _buildContentCard(),
          const SizedBox(height: 24),
          _buildMetadataCard(),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildTypeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  widget.entry.type.icon,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Entry Type',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.entry.type.displayName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Title',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.copy, size: 20),
                  onPressed: () => _copyToClipboard(widget.entry.title, 'Title'),
                  tooltip: 'Copy title',
                ),
              ],
            ),
            const SizedBox(height: 8),
            SelectableText(
              widget.entry.title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentCard() {
    final color = Color(int.parse(widget.entry.type.colorHex.substring(1), radix: 16) + 0xFF000000);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.lock_outline,
                    color: color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _getContentLabel(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.darkGray,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (widget.entry.type == SecureEntryType.loginCredentials ||
                    widget.entry.type == SecureEntryType.workCredentials)
                  IconButton(
                    icon: Icon(
                      _obscureContent ? Icons.visibility_rounded : Icons.visibility_off_rounded,
                      color: AppTheme.mediumGray,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureContent = !_obscureContent;
                      });
                    },
                    tooltip: _obscureContent ? 'Show content' : 'Hide content',
                  ),
                IconButton(
                  icon: const Icon(
                    Icons.copy_rounded,
                    color: AppTheme.mediumGray,
                  ),
                  onPressed: () => _copyToClipboard(widget.entry.content, 'Content'),
                  tooltip: 'Copy content',
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.lightGray,
                borderRadius: BorderRadius.circular(12),
              ),
              child: SelectableText(
                _obscureContent ? _obscureText(widget.entry.content) : widget.entry.content,
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.darkGray,
                  height: 1.5,
                  fontFamily: (widget.entry.type == SecureEntryType.loginCredentials ||
                              widget.entry.type == SecureEntryType.workCredentials) ? 'monospace' : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildMetadataRow('Created', _formatDateTime(widget.entry.createdAt)),
            const SizedBox(height: 8),
            _buildMetadataRow('Last Updated', _formatDateTime(widget.entry.updatedAt)),
            if (widget.entry.id != null) ...[
              const SizedBox(height: 8),
              _buildMetadataRow('Entry ID', widget.entry.id.toString()),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  // Helper methods
  String _getContentLabel() {
    switch (widget.entry.type) {
      case SecureEntryType.personalNote:
        return 'Personal Note';
      case SecureEntryType.loginCredentials:
        return 'Login Credentials';
      case SecureEntryType.paymentCard:
        return 'Payment Card Details';
      case SecureEntryType.identityDocument:
        return 'Identity Document';
      case SecureEntryType.secureNote:
        return 'Secure Note';
      case SecureEntryType.bankAccount:
        return 'Bank Account Details';
      case SecureEntryType.socialMedia:
        return 'Social Media Account';
      case SecureEntryType.workCredentials:
        return 'Work Credentials';
      case SecureEntryType.personalInfo:
        return 'Personal Information';
      case SecureEntryType.other:
        return 'Content';
    }
  }

  String _obscureText(String text) {
    return '•' * text.length;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$label copied to clipboard'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'copy_title':
        _copyToClipboard(widget.entry.title, 'Title');
        break;
      case 'copy_content':
        _copyToClipboard(widget.entry.content, 'Content');
        break;
      case 'edit':
        _editEntry();
        break;
      case 'share':
        _shareEntry();
        break;
    }
  }

  void _shareEntry() {
    // Note: In a real app, you might want to be more careful about sharing sensitive data
    final shareText = '''
${widget.entry.title}

Type: ${widget.entry.type.displayName}
Content: ${(widget.entry.type == SecureEntryType.loginCredentials ||
           widget.entry.type == SecureEntryType.workCredentials) ? '[Hidden for security]' : widget.entry.content}

Created: ${_formatDateTime(widget.entry.createdAt)}
''';

    // For now, just copy to clipboard as sharing sensitive data should be done carefully
    _copyToClipboard(shareText, 'Entry details');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Entry'),
        content: const Text(
          'Entry details have been copied to clipboard. Please be careful when sharing sensitive information.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _editEntry() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEntryScreen(entryToEdit: widget.entry),
      ),
    ).then((result) {
      // Refresh the screen if entry was updated
      if (result == true) {
        // In a real app, you might want to refresh the entry data
        setState(() {});
      }
    });
  }
}
