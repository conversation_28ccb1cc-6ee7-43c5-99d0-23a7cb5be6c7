import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/data_provider.dart';
import '../models/secure_entry.dart';

/// Screen for adding new secure entries or editing existing ones
/// Includes form validation and encryption before storage
class AddEntryScreen extends StatefulWidget {
  final SecureEntry? entryToEdit;

  const AddEntryScreen({Key? key, this.entryToEdit}) : super(key: key);

  @override
  State<AddEntryScreen> createState() => _AddEntryScreenState();
}

class _AddEntryScreenState extends State<AddEntryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  
  SecureEntryType _selectedType = SecureEntryType.personalNote;
  bool _isLoading = false;
  bool _obscureContent = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.entryToEdit != null) {
      final entry = widget.entryToEdit!;
      _titleController.text = entry.title;
      _contentController.text = entry.content;
      _selectedType = entry.type;
      _obscureContent = entry.type == SecureEntryType.loginCredentials;
    } else {
      _obscureContent = _selectedType == SecureEntryType.loginCredentials;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.entryToEdit != null ? 'Edit Entry' : 'Add Entry'),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveEntry,
              child: const Text('Save'),
            ),
        ],
      ),
      body: _buildForm(),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTypeSelector(),
            const SizedBox(height: 24),
            _buildTitleField(),
            const SizedBox(height: 16),
            _buildContentField(),
            const SizedBox(height: 24),
            _buildSaveButton(),
            if (widget.entryToEdit != null) ...[
              const SizedBox(height: 16),
              _buildDeleteButton(),
            ],
            const SizedBox(height: 32), // Extra padding at bottom
          ],
        ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Entry Type',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: SecureEntryType.values.map((type) {
                final isSelected = _selectedType == type;
                return FilterChip(
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(type.icon),
                      const SizedBox(width: 4),
                      Text(type.displayName),
                    ],
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedType = type;
                      _obscureContent = type == SecureEntryType.loginCredentials;
                    });
                  },
                  selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      decoration: InputDecoration(
        labelText: 'Title',
        hintText: 'Enter a title for this entry',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        prefixIcon: const Icon(Icons.title),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a title';
        }
        if (value.trim().length < 2) {
          return 'Title must be at least 2 characters';
        }
        return null;
      },
      textCapitalization: TextCapitalization.words,
    );
  }

  Widget _buildContentField() {
    return TextFormField(
      controller: _contentController,
      decoration: InputDecoration(
        labelText: _getContentLabel(),
        hintText: _getContentHint(),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        prefixIcon: Icon(_getContentIcon()),
        suffixIcon: _selectedType == SecureEntryType.loginCredentials
            ? IconButton(
                icon: Icon(_obscureContent ? Icons.visibility : Icons.visibility_off),
                onPressed: () {
                  setState(() {
                    _obscureContent = !_obscureContent;
                  });
                },
              )
            : null,
      ),
      obscureText: _obscureContent,
      maxLines: (_selectedType == SecureEntryType.personalNote ||
                 _selectedType == SecureEntryType.secureNote) ? 5 : 1,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter the content';
        }
        return null;
      },
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _saveEntry,
      icon: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.save),
      label: Text(_isLoading ? 'Saving...' : 'Save Entry'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildDeleteButton() {
    return OutlinedButton.icon(
      onPressed: _isLoading ? null : _deleteEntry,
      icon: const Icon(Icons.delete, color: Colors.red),
      label: const Text('Delete Entry', style: TextStyle(color: Colors.red)),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        side: const BorderSide(color: Colors.red),
      ),
    );
  }

  // Helper methods
  String _getContentLabel() {
    switch (_selectedType) {
      case SecureEntryType.personalNote:
        return 'Personal Note';
      case SecureEntryType.loginCredentials:
        return 'Login Credentials';
      case SecureEntryType.paymentCard:
        return 'Payment Card Details';
      case SecureEntryType.identityDocument:
        return 'Identity Document';
      case SecureEntryType.secureNote:
        return 'Secure Note';
      case SecureEntryType.bankAccount:
        return 'Bank Account Details';
      case SecureEntryType.socialMedia:
        return 'Social Media Account';
      case SecureEntryType.workCredentials:
        return 'Work Credentials';
      case SecureEntryType.personalInfo:
        return 'Personal Information';
      case SecureEntryType.other:
        return 'Content';
    }
  }

  String _getContentHint() {
    switch (_selectedType) {
      case SecureEntryType.personalNote:
        return 'Enter your personal note here...';
      case SecureEntryType.loginCredentials:
        return 'Username: <EMAIL>\nPassword: ********';
      case SecureEntryType.paymentCard:
        return 'Card Number: **** **** **** ****\nExpiry: MM/YY\nCVV: ***';
      case SecureEntryType.identityDocument:
        return 'Document Number: ********\nIssue Date: DD/MM/YYYY';
      case SecureEntryType.secureNote:
        return 'Enter your secure note here...';
      case SecureEntryType.bankAccount:
        return 'Account Number: ********\nRouting Number: ********';
      case SecureEntryType.socialMedia:
        return 'Platform: Instagram\nUsername: @username\nPassword: ********';
      case SecureEntryType.workCredentials:
        return 'Company: Example Corp\nEmail: <EMAIL>\nPassword: ********';
      case SecureEntryType.personalInfo:
        return 'Name: John Doe\nPhone: +**********\nAddress: 123 Main St';
      case SecureEntryType.other:
        return 'Enter content...';
    }
  }

  IconData _getContentIcon() {
    switch (_selectedType) {
      case SecureEntryType.personalNote:
        return Icons.note_alt;
      case SecureEntryType.loginCredentials:
        return Icons.login;
      case SecureEntryType.paymentCard:
        return Icons.credit_card;
      case SecureEntryType.identityDocument:
        return Icons.badge;
      case SecureEntryType.secureNote:
        return Icons.lock_outline;
      case SecureEntryType.bankAccount:
        return Icons.account_balance;
      case SecureEntryType.socialMedia:
        return Icons.share;
      case SecureEntryType.workCredentials:
        return Icons.work;
      case SecureEntryType.personalInfo:
        return Icons.person;
      case SecureEntryType.other:
        return Icons.text_fields;
    }
  }

  Future<void> _saveEntry() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final dataProvider = Provider.of<DataProvider>(context, listen: false);
      
      final entry = SecureEntry(
        id: widget.entryToEdit?.id,
        title: _titleController.text.trim(),
        content: _contentController.text.trim(),
        type: _selectedType,
        createdAt: widget.entryToEdit?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      bool success;
      if (widget.entryToEdit != null) {
        success = await dataProvider.updateEntry(entry);
      } else {
        success = await dataProvider.addEntry(entry);
      }

      if (success && mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.entryToEdit != null
                ? 'Entry updated successfully'
                : 'Entry added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(dataProvider.errorMessage ?? 'Failed to save entry'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteEntry() async {
    if (widget.entryToEdit?.id == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Entry'),
        content: Text('Are you sure you want to delete "${widget.entryToEdit!.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    final dataProvider = Provider.of<DataProvider>(context, listen: false);

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await dataProvider.deleteEntry(widget.entryToEdit!.id!);

      if (success && mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Entry deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(dataProvider.errorMessage ?? 'Failed to delete entry'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
